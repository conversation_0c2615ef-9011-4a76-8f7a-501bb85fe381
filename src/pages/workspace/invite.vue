<script setup lang="ts">
import InviteCard from '@/components/workspace/inviteCard.vue'
import { useAuthStore } from '@/stores/auth'
import { useUsersStore } from '@/stores/user'
import { useInviteLinkStore } from '@/stores/workspace'
import { onMounted, ref } from 'vue'

// 获取用户信息和邀请码相关功能
const inviteLinkStore = useInviteLinkStore()
const authStore = useAuthStore()
const usersStore = useUsersStore()
const { getInviteLinks, createInviteLinkForJoiningCompany, enableInviteLink, disableInviteLink } = inviteLinkStore
const showQrCode = ref()
const qrcodeDataUrl = ref('')
const { userRoleInfo } = storeToRefs(authStore)
// 列表状态
const isLoading = ref(false)
const inviteLinks = ref([]) // 初始化为空数组
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10) // 每页显示10条数据
const isLoadingMore = ref(false) // 是否正在加载更多数据
const hasMoreData = ref(true) // 是否还有更多数据
const shareParams = ref({
  title: '',
  path: '',
})
// 创建邀请码相关
const createInvite = ref()
const inviteCount = ref(1)
const remark = ref('')
const formError = ref('')
const companyId = ref('')
const uid = ref('')
const inviteAllType = ref([{ key: 'invite_to_create_company', values: '空间所有者' }, { key: 'invite_to_manage_company', values: '空间管理员' }, { key: 'invite_to_join_company', values: '会员' }])
const inviteType = ref([{ key: 'invite_to_join_company', values: '会员' }])
const selectInviteType = ref('')
// 获取当前用户的公司ID
onMounted(async () => {
  try {
    // 获取当前用户信息
    await fetchUser()

    await loadInviteLinks()
  }
  catch (error) {
    console.error('获取空间ID失败', error)
  }
})
function closeQRCodeModal() {
  showQrCode.value.close()
  qrcodeDataUrl.value = ''
}
function CreateQrCode(base64Img: string) {
  qrcodeDataUrl.value = base64Img
  showQrCode.value.open()
}
// 获取当前用户数据
async function fetchUser() {
  const result = await usersStore.getCurrentUserInfo()
  if (result) {
    companyId.value = result.current_company_id
    uid.value = result.uid
  }
}

// 加载邀请码列表
async function loadInviteLinks() {
  try {
    isLoading.value = true

    const { data, count } = await getInviteLinks(currentPage.value, pageSize.value)
    if (currentPage.value === 1) {
      inviteLinks.value = data
    }
    else {
      inviteLinks.value = [...inviteLinks.value, ...data]
    }
    totalCount.value = count || 0

    // 判断是否还有更多数据
    hasMoreData.value = inviteLinks.value.length < totalCount.value
  }
  catch (error) {
    console.error('加载邀请码失败', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  }
  finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

// 加载更多数据
async function loadMore() {
  if (isLoadingMore.value || !hasMoreData.value) { return }
  isLoadingMore.value = true
  currentPage.value++
  await loadInviteLinks()
}

// 打开创建邀请码弹窗
function openCreateModal() {
  createInvite.value.open()
  inviteCount.value = 1
  formError.value = ''
}

// 关闭创建邀请码弹窗
function dialogClose() {
  createInvite.value.close()
}

// 创建邀请码
async function createInviteLink() {
  if (!companyId.value) {
    formError.value = '未找到当前空间，请重试'
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (inviteCount.value < 1) {
    formError.value = '邀请数量必须大于0'
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    isLoading.value = true

    // 显示加载中
    uni.showLoading({ title: '正在创建邀请码...' })

    // 调用创建邀请码的API
    await createInviteLinkForJoiningCompany(
      selectInviteType.value.key,
      companyId.value,
      inviteCount.value,
      remark.value,
      uid.value,
    )

    // 显示成功提示
    uni.hideLoading()
    uni.showToast({
      title: '邀请码创建成功',
      icon: 'success',
      duration: 2000,
    })

    // 关闭弹窗并刷新列表
    dialogClose()
    currentPage.value = 1
    await loadInviteLinks()
  }
  catch (error) {
    uni.hideLoading()
    formError.value = '创建邀请码失败，请重试'
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
    console.error('创建邀请码失败', error)
  }
  finally {
    isLoading.value = false
  }
}

async function handleToggleStatus(id: string, newStatus: boolean) {
  // 这里可以添加实际的状态切换请求逻辑，如调用 API
  await inviteLinkStore.toggleInviteLinkStatus(id, !newStatus)

  await loadInviteLinks()
}
function inviteTypeChange(e: any) {
  selectInviteType.value = userRoleInfo?.value?.is_owner ? inviteAllType.value[e.detail.value] : inviteType.value[e.detail.value]
}
// 处理复制邀请链接的方法
function handleCopyInviteLink(inviteCode: string) {
  uni.setClipboardData({
    data: inviteCode,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
        duration: 2000,
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000,
      })
    },
  })
}
function setSharePrams(title: string, path: string) {
  console.log(title, path, 'dfsdfsdfsdfsdfsdfsdfds')

  shareParams.value.title = title || '邀请您加入空间'
  shareParams.value.path = path || ''
}

onShareAppMessage((_res) => {
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share11.png', // 分享图
    // desc: '小程序描述描述描述描述',
  }
})

onShareTimeline(() => {
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share11.png', // 分享图
  }
})
</script>

<template>
  <view class="invite-page">
    <!-- 头部区域 -->
    <view class="header flex-1">
      <view class="title-container">
        <text class="title">邀请码管理</text>
        <text class="subtitle">管理您的空间邀请码</text>
      </view>
      <view>
        <button class="create-button" @click="openCreateModal">
          <uni-icons type="plusempty" size="18" color="#FFFFFF" />
          <text>创建邀请码</text>
        </button>
      </view>
    </view>

    <!-- 列表区域 -->
    <scroll-view class="list-container h-[600 px] flex-auto" scroll-y="true" @scrolltolower="loadMore">
      <view v-for="inviteLink in inviteLinks" :key="inviteLink.id" class="space-y-2">
        <InviteCard
          :item="inviteLink" @toggle-status="handleToggleStatus" @copy-invite-link="handleCopyInviteLink"
          @show-qrcode="CreateQrCode" @set-share-prams="setSharePrams"
        />
      </view>
      <!-- 加载更多提示 -->
      <view v-if="isLoadingMore" class="py-2 text-center">
        <uni-icons type="loading" size="20" /> 加载中...
      </view>
      <!-- 没有更多数据提示 -->
      <view v-if="!hasMoreData && inviteLinks.length > 0" class="py-2 text-center">
        没有更多数据了
      </view>
    </scroll-view>
  </view>

  <uni-popup ref="createInvite" type="dialog">
    <uni-popup-dialog
      type="dialog" cancelText="关闭" confirmText="创建" title="创建邀请码" @confirm="createInviteLink"
      @close="dialogClose"
    >
      <template #default>
        <view class="modal-body">
          <view class="form-group">
            <label class="form-label">邀请数量</label>
            <view class="input-container">
              <uni-icons type="personadd" size="18" color="#2563EB" class="input-icon" />
              <input v-model.number="inviteCount" type="number" class="form-input" placeholder="请输入数量" min="1">
            </view>
            <view class="form-tip">
              <uni-icons type="info" size="14" color="#6B7280" />
              <text>设置链接最多可以邀请的人数</text>
            </view>
          </view>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <label class="form-label">权限</label>
            <view class="input-container">
              <uni-icons type="auth" size="18" color="#2563EB" class="input-icon" />
              <picker
                :value="selectInviteType" :range="userRoleInfo?.value?.is_owner ? inviteAllType : inviteType"
                range-key="values" @change="inviteTypeChange"
              >
                <view class="flex w-32 text-sm">
                  {{ selectInviteType ? selectInviteType.values : "会员"
                  }}
                </view>
              </picker>
            </view>
          </view>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <label class="form-label">备注</label>
            <view class="input-container">
              <uni-icons type="chat" size="18" color="#2563EB" class="input-icon" />
              <input v-model.string="remark" type="string" class="form-input" placeholder="请输入备注" min="1">
            </view>
          </view>
        </view>
      </template>
    </uni-popup-dialog>
  </uni-popup>

  <uni-popup
    ref="showQrCode" type="center" background-color="#fff" borderRadius="50px 50px 50px 50px"
    :safe-area="true"
  >
    <view class="relative rounded-md bg-white p-4">
      <image :src="qrcodeDataUrl" style="width: 200px; height: 200px" />
      <view class="absolute right-2 top-2" @click="closeQRCodeModal">
        <text class="cursor-pointer text-xl text-gray-500">&times;</text>
      </view>
    </view>
  </uni-popup>
</template>

<style scoped>
/* 页面整体样式 */
.invite-page {
  @apply bg-blue-50 min-h-screen p-3;
}

/* 头部区域 */
.header {
  @apply flex items-center justify-between mb-6;
}

.title-container {
  @apply flex flex-col;
}

.title {
  @apply text-2xl font-semibold text-blue-900;
}

.subtitle {
  @apply text-sm text-blue-600 mt-1;
}

.create-button {
  @apply flex items-center space-x-2 bg-blue-600 text-white px-4 py-2.5 rounded-lg shadow-sm hover:bg-blue-700 transition-colors duration-200;
}

/* 列表容器 */
.list-container {
  @apply bg-white rounded-xl shadow-md overflow-hidden border border-blue-100 flex-auto space-y-2;
}

.list-header {
  @apply flex items-center py-4 px-3 bg-blue-50 border-b border-blue-100 text-blue-800 font-medium;
}

.loading-container {
  @apply flex flex-col items-center justify-center py-16;
}

.loading-spinner {
  @apply animate-spin;
}

.loading-text {
  @apply mt-3 text-sm text-blue-600 font-medium;
}

.empty-container {
  @apply flex flex-col items-center justify-center py-16;
}

.empty-icon {
  @apply bg-blue-50 p-4 rounded-full mb-3;
}

.empty-text {
  @apply text-base text-blue-800 font-medium mb-4;
}

.empty-action {
  @apply text-sm text-blue-600 hover:text-blue-800 underline;
}

.list-content {
  @apply divide-y divide-blue-50;
}

.list-item {
  @apply flex items-center py-4 px-6 hover:bg-blue-50 transition-colors duration-200;
}

.item-cell {
  @apply text-sm text-gray-700;
}

.link-cell {
  @apply flex items-center;
}

.link-text {
  @apply truncate mr-2 text-blue-700;
}

.date-cell {
  @apply text-gray-600;
}

.limit-text {
  @apply font-medium;
}

.copy-button {
  @apply p-1.5 rounded-full hover:bg-blue-100 transition-colors duration-200;
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium space-x-1;
}

.status-dot {
  @apply w-2 h-2 rounded-full;
}

.dot-active {
  @apply bg-green-500;
}

.dot-disabled {
  @apply bg-gray-400;
}

.status-active {
  @apply bg-green-100 text-green-800;
}

.status-disabled {
  @apply bg-gray-100 text-gray-600;
}

.action-cell {
  @apply flex items-center space-x-2;
}

.action-button {
  @apply flex items-center space-x-1 text-xs px-2.5 py-1.5 rounded-md transition-colors duration-200;
}

.enable-button {
  @apply bg-blue-50 text-blue-700 hover:bg-blue-100;
}

.disable-button {
  @apply bg-gray-50 text-gray-700 hover:bg-gray-100;
}

.copy-action {
  @apply bg-blue-50 text-blue-700 hover:bg-blue-100;
}

/* 分页 */
.pagination {
  @apply flex items-center justify-center py-5 border-t border-blue-50;
}

.page-button {
  @apply flex items-center space-x-1 px-3 py-1.5 text-sm rounded-md bg-white border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors duration-200;
}

.page-disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-white;
}

.page-info {
  @apply flex items-center mx-4 text-sm;
}

.current-page {
  @apply text-blue-700 font-medium;
}

.page-divider {
  @apply text-gray-400 mx-1;
}

.total-pages {
  @apply text-gray-500;
}

/* 弹窗 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-xl shadow-xl w-full max-w-md mx-4 overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-5 border-b border-blue-100;
}

.modal-title-container {
  @apply flex items-center space-x-2;
}

.modal-title {
  @apply text-lg font-medium text-blue-900;
}

.close-button {
  @apply p-1.5 rounded-full hover:bg-gray-100 transition-colors duration-200;
}

.modal-body {
  @apply p-1;
}

.modal-footer {
  @apply flex items-center justify-end p-5 border-t border-blue-100 space-x-3;
}

.cancel-button {
  @apply px-4 py-2.5 bg-gray-100 text-gray-700 transition-colors duration-200;
}

.confirm-button {
  @apply flex items-center space-x-2 px-4 py-2.5 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200;
}

/* 表单组件 */
.form-group {
  @apply mb-1;
}

.form-label {
  @apply block text-sm font-medium text-blue-900 mb-2;
}

.form-tip {
  @apply mt-2 text-xs text-gray-500 flex items-center space-x-1;
}

.input-container {
  @apply flex items-center w-full px-3 py-2.5 border border-blue-200 rounded-lg bg-white focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500 transition-all duration-200;
}

.input-icon {
  @apply mr-2;
}

.form-input {
  @apply w-full bg-transparent focus:outline-none text-gray-800;
}
</style>
