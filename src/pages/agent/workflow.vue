<script setup lang="ts">
import ChatTitle from '@/components/chat/ChatTitle.vue'
import RechargeTips from '@/components/chat/rechargeTips.vue'
import CompletionInput from '@/components/completion/CompletionInput.vue'
import CompletionText from '@/components/completion/CompletionText.vue'
import { useAuthStore } from '@/stores/auth'
import { useDifyChatStore } from '@/stores/dify-chat'
import { computed, nextTick, onMounted, ref, watch } from 'vue'

const props = defineProps({
  id: String,
  agentName: String,
  type: String,
})

const titleHeight = ref(0)

const animationState = ref('closed')
const scrollInterval = ref<ReturnType<typeof setInterval> | null>(null)

const authStore = useAuthStore()
const difyStore = useDifyChatStore()
const scrollViewRef = ref(null)

const { user } = storeToRefs(authStore)
// 从 dify store 中解构出所需的方法
const {
  initDifyApp, // 初始化 Dify 应用
  setAppId, // 设置应用 ID
  setType, // 设置类型
  startNewConversation, // 开始新对话
} = difyStore
if (props.id) {
  setAppId(props.id)
}
const { rechargeTips, workflowContent } = storeToRefs(difyStore)
// 滚动到底部
function scrollToBottom() {
  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    setTimeout(() => {
      // #ifdef H5
      // H5环境下使用页面滚动
      uni.pageScrollTo({
        scrollTop: 99999,
        duration: 300,
      })
      // #endif

      // #ifdef MP-WEIXIN
      // 微信小程序环境下使用选择器查询获取scroll-view
      const query = uni.createSelectorQuery()
      query.select('#chatMessages').boundingClientRect(() => {})
      query.selectViewport().scrollOffset(() => {})
      query.exec((res) => {
        if (res && res[0]) {
          // 使用scroll-view的scrollTop属性
          uni.createSelectorQuery()
            .select('#chatMessages')
            .node(() => {})
            .exec((scrollRes) => {
              if (scrollRes && scrollRes[0] && scrollRes[0].node) {
                scrollRes[0].node.scrollTop = scrollRes[0].node.scrollHeight
              }
              else {
                // 如果无法获取node，回退到pageScrollTo
                uni.pageScrollTo({
                  scrollTop: 99999,
                  duration: 300,
                })
              }
            })
        }
      })
      // #endif

      // #ifndef H5 || MP-WEIXIN
      // 其他平台使用通用方法
      uni.pageScrollTo({
        scrollTop: 99999,
        duration: 300,
      })
      // #endif
    }, 100)
  })
}
// 注意：自动滚动功能已移除，因为它依赖于未定义的变量
// 设置自动滚动
function setupAutoScroll() {
  // 清除已存在的定时器
  if (scrollInterval.value) {
    clearInterval(scrollInterval.value)
    scrollInterval.value = null
  }

  // 如果正在加载，设置定时滚动
  if (isLoadingMessage.value) {
    scrollInterval.value = setInterval(scrollToBottom, 1000)
  }
}

// 从 store 中获取响应式状态
const {
  messages,
  isLoadingApp,
  isRequesting,
  isLoadingMessage, // 是否正在加载消息
  currentConversationId, // 当前会话 ID
  conversationHistory, // 会话历史列表
} = storeToRefs(difyStore)
const showMessage = ref()
// 计算当前会话标题
const currentChatTitle = computed(() => {
  if (!currentConversationId.value) {
    return props.agentName || 'AI聊天助手'
  }

  // 从会话历史中查找当前会话
  const currentChat = conversationHistory.value.find(chat => chat.id === currentConversationId.value)
  return currentChat?.name || props.agentName || 'AI聊天助手'
})

// 监听消息变化，当加载新会话时滚动到底部
watch(isLoadingMessage, (newValue) => {
  console.log(newValue, 'newValuenewValuenewValuenewValue')

  if (newValue) {
    setupAutoScroll()
  }
  else {
    // 停止加载时清除定时器
    if (scrollInterval.value) {
      clearInterval(scrollInterval.value)
      scrollInterval.value = null
    }
    // 最后再滚动一次确保显示最新消息
    nextTick(() => {
      scrollToBottom()
    })
  }
})
// 分享相关数据
const shareParams = ref({
  title: '',
  path: '',
  imageUrl: '',
  desc: '',

})

// 发送消息

// 处理消息分享
function handleShareMessage(data: { content: string, shareType: string }) {
  // 设置要分享的内容
  shareParams.value.desc = data.content
  shareParams.value.title = `${props.agentName || '智能助手'}的回复`
  shareParams.value.path = `/pages/agent/ai?id=${props.id}&agentName=${props.agentName}`

  // 根据分享类型执行不同操作
  if (data.shareType === '微信好友' || data.shareType === '朋友圈') {
    // 微信分享会自动触发 onShareAppMessage 或 onShareTimeline
    uni.showShareMenu({
      withShareTicket: true,
      menuList: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        uni.showToast({
          title: '请点击右上角分享',
          icon: 'none',
        })
      },
    })
  }
  else if (data.shareType === '复制链接') {
    // 复制分享链接
    const shareLink = `${props.agentName}: ${data.content}`
    uni.setClipboardData({
      data: shareLink,
      success: () => {
        uni.showToast({
          title: '链接已复制',
          icon: 'none',
        })
      },
    })
  }
}
function openDrawer() {
  showMessage.value.open()
}

onShareAppMessage((_res) => {
  shareParams.value.title = `${props.agentName || '智能助手'}`
  shareParams.value.path = `/pages/agent/workflow?id=${props.id}&agentName=${props.agentName}&conversationId=${currentConversationId.value}`
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share.jpg', // 分享图
    // desc: '小程序描述描述描述描述',
  }
})

onShareTimeline(() => {
  shareParams.value.title = `${props.agentName || '智能助手'}`
  shareParams.value.path = `/pages/agent/workflow?id=${props.id}&agentName=${props.agentName}`
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share.jpg', // 分享图
  }
})
onLoad(async () => {
  // 初始化逻辑
  // 设置默认分享路径

  try {
    // 可以在这里添加页面加载时的逻辑
    await setType('workflow')

    // 初始化 Dify 应用
    await initDifyApp()

    // 如果有消息，滚动到底部
  }
  catch (_error) {
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none',
    })
  }
})

function setTitleHeight(int: number) {
  titleHeight.value = int
}

// 处理充值提示窗口关闭
function handleRechargeTipsClose() {
  rechargeTips.value = false
}

// 组件卸载时清理定时器
onMounted(() => {
  if (scrollInterval.value) {
    clearInterval(scrollInterval.value)
    scrollInterval.value = null
  }
})
onUnmounted(() => {
  difyStore.clearAll()
})
watch(
  user,
  async (userValue, oldValue) => {
    if (userValue && userValue.id) {
      console.log('userValue && userValue.id', (userValue && userValue.id))
      difyStore.getCompletionSaved()
    }
  },
  { immediate: true },
)

// 监听生成内容变化，自动打开抽屉
watch(workflowContent, (newValue) => {
  console.log(newValue, 'workflowContentworkflowContent')

  showMessage.value.open()
})
</script>

<template>
  <view class="chat-page">
    <!-- 主聊天区域 -->
    <view
      id="chatbox" class="chat-main-container"
      :style="{ transform: animationState === 'opened' ? 'scale(0.95) translateX(300px)' : '' }"
    >
      <!-- 聊天标题栏 -->
      <ChatTitle :title="currentChatTitle" @setTitleHeight="setTitleHeight" />

      <!-- 聊天消息区域 -->
      <uni-load-more v-if="isLoadingApp" status="loading" />
      <view id="chatMessages" ref="scrollViewRef" class="chat-messages-container">
        <CompletionInput @openDrawer="openDrawer" />
      </view>
      <RechargeTips v-if="rechargeTips" :visible="rechargeTips" :app-id="props.id" @close="handleRechargeTipsClose" />
      <!-- 消息输入区域 -->
    </view>
    <uni-popup ref="showMessage" type="bottom" background-color="#fff" borderRadius="30px 30px 0px 0px">
      <view class="flex h-[calc(100vh-(200px))] w-full flex-col overflow-hidden rounded-t-xl bg-white pt-4">
        <view v-if="workflowContent" class="mx-auto py-2 text-sm text-gray-500">
          已生成 {{ workflowContent.length }} 个字符
        </view>
        <view class="flex-1 overflow-auto px-4">
          <CompletionText />
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<style scoped lang="scss">
@import url(./chat.scss);
</style>
