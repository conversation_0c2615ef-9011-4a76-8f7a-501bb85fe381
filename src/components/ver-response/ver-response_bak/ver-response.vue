<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { convertLatexToKaTeX } from './converLatex.js'
import MpHtml from './mp-html/mp-html'
import { THEME_PRESETS } from './themePresets'

// Props 定义
const props = defineProps({
  themeColor: {
    type: String,
    default: '',
  },
  theme: {
    type: String,
    default: DEFAULT_THEME,
    validator: (value) => {
      const availableThemes = Object.keys(THEME_PRESETS)
      const isValid = availableThemes.includes(value)
      if (!isValid && process.env.NODE_ENV === 'development') {
        console.warn(
          `[Theme Validation] 无效主题 "${value}", 可用选项: ${availableThemes.join(', ')}`,
        )
      }
      return isValid
    },
  },
  content: {
    type: String,
    default: '',
  },
})
// 事件
const emit = defineEmits(['load'])
const DEFAULT_THEME = 'vs'
const DEFAULT_TAG_STYLE = {
  pre: `border-radius: 6px; padding: 12px; font-size: 14px; overflow-x: auto;`,
  code: `font-family: monospace; font-size: 14px;`,
}
const containerStyle = ""
// 组件名称
const name = 'VerResponse'

// 响应式数据
const processedContent = ref('')
const baseTagStyle = ref(DEFAULT_TAG_STYLE)
const loadingImg = ref(undefined) // 添加缺失的loadingImg变量

// 计算属性
const computedTagStyle = computed(() => {
  const themeConfig = THEME_PRESETS[props.theme] || THEME_PRESETS[DEFAULT_THEME]
  const colors = {
    ...themeConfig.colors,
    primary: props.themeColor || themeConfig.colors.primary,
  }

  return {
    ...baseTagStyle.value,
    ...themeConfig.styles(colors),
  }
})

// 处理内容方法
function processContent(content) {
  console.log(content,"contentcontent");

  if (!content) {
    processedContent.value = ''
    return
  }

  const parts = content.split('```')
  const resultParts = []

  for (let i = 0; i < parts.length; i++) {
    if (i % 2 === 1) {
      resultParts.push(`\`\`\`${parts[i]}\`\`\``)
    }
    else {
      resultParts.push(processLatex(parts[i]))
    }
  }

  processedContent.value = resultParts.join('')
}

// 处理LaTeX方法
function processLatex(str) {
  return str
    .replace(/\\\[([\s\S]*?)\\\]/g, '$$$\n$1\n$$$')
    .replace(/\\\(([\s\S]*?)\\\)/g, '$ $1 $')
    .replace(/\${1,2}([\s\S]*?)\${1,2}/g, (match, p1) => {
      const delimiter = match.includes('$$') ? '$$' : '$'
      if (!p1.includes('begin')) {
        return `${delimiter}${p1}${delimiter}`
      }
      return `${delimiter}${convertLatexToKaTeX(p1)}${delimiter}`
    })
}

// 加载完成处理方法
function handleLoad() {
  emit('load')
}

// 监听内容变化
watch(
  () => props.content,
  (newContent) => {
    processContent(newContent)
  },
  { immediate: true },
)
</script>

<template>
  <view class="ver-response">
    <MpHtml
      :content="processedContent" :markdown="true" :selectable="true" :scroll-table="true" :show-img-menu="true"
      :loading-img="loadingImg" :lazy-load="true" :tag-style="computedTagStyle" :container-style="containerStyle"
      @load="handleLoad"
    />
  </view>
</template>

<style>
.ver-response {
  @apply max-w-full;
}
</style>
