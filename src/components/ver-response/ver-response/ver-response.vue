<script>
import { convertLatexToKaTeX } from './converLatex.js'
import mpHtml from './mp-html/mp-html'
import { THEME_PRESETS } from './themePresets'

const DEFAULT_THEME = 'vs'
const DEFAULT_TAG_STYLE = {
  pre: `border-radius: 6px; padding: 12px; font-size: 14px; overflow-x: auto;`,
  code: `font-family: monospace; font-size: 14px;`,

}

export default {
  name: 'VerResponse',
  components: {
    MpHtml: mpHtml,
  },

  props: {
    themeColor: {
      type: String,
      default: '',
    },
    theme: {
      type: String,
      default: DEFAULT_THEME,
      validator: (value) => {
        const availableThemes = Object.keys(THEME_PRESETS)
        const isValid = availableThemes.includes(value)
        if (!isValid && process.env.NODE_ENV === 'development') {
          console.warn(
            `[Theme Validation] 无效主题 "${value}", 可用选项: ${availableThemes.join(', ')}`,
          )
        }
        return isValid
      },
    },
    content: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      processedContent: '',
      baseTagStyle: DEFAULT_TAG_STYLE,
    }
  },

  computed: {
    computedTagStyle() {
      const themeConfig = THEME_PRESETS[this.theme] || THEME_PRESETS[DEFAULT_THEME]
      const colors = {
        ...themeConfig.colors,
        primary: this.themeColor || themeConfig.colors.primary,
      }

      return {
        ...this.baseTagStyle,
        ...themeConfig.styles(colors),
      }
    },
  },

  watch: {
    content: {
      immediate: true,
      handler(newContent) {
        this.processContent(newContent)
      },
    },
  },

  methods: {
    processContent(content) {
      if (!content) {
        this.processedContent = ''
        return
      }

      const parts = content.split('```')
      const resultParts = []

      for (let i = 0; i < parts.length; i++) {
        if (i % 2 === 1) {
          resultParts.push(`\`\`\`${parts[i]}\`\`\``)
        }
        else {
          resultParts.push(this.processLatex(parts[i]))
        }
      }

      this.processedContent = resultParts.join('')
    },

    processLatex(str) {
      return str
        .replace(/\\\[([\s\S]*?)\\\]/g, '$$$\n$1\n$$$')
        .replace(/\\\(([\s\S]*?)\\\)/g, '$ $1 $')
        .replace(/\${1,2}([\s\S]*?)\${1,2}/g, (match, p1) => {
          const delimiter = match.includes('$$') ? '$$' : '$'
          if (!p1.includes('begin')) {
            return `${delimiter}${p1}${delimiter}`
          }
          return `${delimiter}${convertLatexToKaTeX(p1)}${delimiter}`
        })
    },

    handleLoad() {
      this.$emit('load')
    },
  },
}
</script>

<template>
  <view class="ver-response">
    <MpHtml
      :content="processedContent"
      :markdown="true"
      :tag-style="computedTagStyle"
      @load="handleLoad"
    />
  </view>
</template>

<style>
.ver-response {
  padding: 12px;
}
</style>
