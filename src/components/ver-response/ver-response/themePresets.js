export const THEME_PRESETS = {
 vsinger: {
    colors: {
      primary: '#0F998F',
      secondary: '#66CCFF',
      border: '#EAECF0'
    },
    styles: (colors) => ({
      p: `
        margin: 8px 0;
        font-size: 16px;
        line-height: 1.8;
        letter-spacing: 0.05em;
        color: ${colors.text};
      `,
      h1: `
        margin: 20px 0;
        padding: 12px 0;
        font-size: 22px;
        font-weight: 700;
        text-align: center;
        color: ${colors.primary};
        background: linear-gradient(to right, transparent 20%, ${colors.primary}20 50%, transparent 80%);
      `,
      h2: `
	    text-align: center;
        margin: 30px 0 15px;
        padding: 8px 20px;
        font-size: 18px;
		font-weight: bolder;
        color: ${colors.primary};
        border-radius: 12px;
      `,
      h3: `
        margin: 25px 0 12px;
        padding-left: 15px;
        font-size: 16px;
        color: ${colors.primary};
        border-left: 3px solid ${colors.primary};
      `,
      blockquote: `
        // margin: 15px 0;
        padding: 2px 7px;
		margin: 10px 0;
		font-size: 15px;
        border-radius: 3px;
        color: #666;
      `,
      ul: `
        margin: 12px 0;
        padding-left: 25px;
        // list-style: none;
      `,
	 
      li: `
        padding: 6px 12px;
        margin: 1px;
      `,
      a: `
        color: ${colors.primary};
        padding: 2px 4px;
        border-radius: 4px;
        text-decoration: none;
        background: ${colors.primary}15;
      `,
      hr: `
        height: 2px;
        margin: 25px 0;
        border: none;
        background: linear-gradient(
          90deg,
          ${colors.primary}00 0%,
          ${colors.primary} 50%,
          ${colors.primary}00 100%
        );
      `,
      table: `
        border-radius: 3px;
        overflow: hidden;
        min-width: 100%;
        border-collapse: collapse;
      `,
      th: `
        background: ${colors.primary}25;
        color: ${colors.primary};
        padding: 12px;
        font-weight: 600;
        text-align: left;
		border: 1px solid ${colors.primary};
      `,
      td: `
        padding: 10px;
        border-bottom: 1px solid ${colors.primary};
        color: ${colors.text};
		border: 1px solid ${colors.primary};
      `,
      pre: `
        margin: 15px 0;
        padding: 12px;
        border-radius: 8px;
        font-size: 14px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      `,
      code: `
        font-family: monospace;
        font-size: 14px;
        padding: 2px 4px;
        border-radius: 4px;
      `,
      img: `
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 10px 0;
        display: block;
      `,
      strong: `
        color: ${colors.primary};
        font-weight: bold;
      `,
      em: `
        color: ${colors.primary};
        font-style: italic;
      `
    })
  },
  heavyAnime: {
    colors: {
      primary: '#4a4a4a',
      text: '#4a4a4a',
      border: '#4A4A4A'
    },
    styles: (colors) => ({
      p: `
        margin: 8px 0;
        font-size: 16px;
        line-height: 1.8;
        letter-spacing: 0.05em;
        color: ${colors.text};
        text-shadow: 0 1px 1px rgba(255,255,255,0.8);
      `,
      h1: `
        margin: 20px 0;
        padding: 12px 0;
        font-size: 22px;
        font-weight: 700;
        text-align: center;
        color: ${colors.primary};
        background: linear-gradient(to right, transparent 20%, ${colors.primary}20 50%, transparent 80%);
        position: relative;
      `,
      h2: `
        margin: 30px 0 15px;
        padding: 8px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        background: ${colors.primary};
        border-radius: 20px;
        display: inline-block;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		  &::before {
		    content: '';
		    background: linear-gradient(45deg, ${colors.primary}, transparent);
		    z-index: -1;
		    border-radius: 14px;
		  }
      `,
      h3: `
        margin: 25px 0 12px;
        padding-left: 15px;
        font-size: 16px;
        color: ${colors.primary};
        position: relative;
		border-left: 3px solid ${colors.primary};
      `,
      blockquote: `
        margin: 15px 0;
        padding: 12px 20px;
        background: ${colors.primary}15;
        border-left: 4px solid ${colors.primary};
        border-radius: 8px;
        color: #666;
        position: relative;
      `,
      ul: `
        margin: 12px 0;
        padding-left: 25px;
        list-style: none;
      `,
      li: `
        padding: 6px 12px;
        color: ${colors.primary};
        background: ${colors.primary}10;
      `,
      a: `
        color: ${colors.primary};
        padding: 2px 4px;
        border-radius: 4px;
        text-decoration: none;
        background: ${colors.primary}15;
      `,
      hr: `
        height: 2px;
        margin: 25px 0;
        border: none;
        background: linear-gradient(
          90deg,
          ${colors.primary}00 0%,
          ${colors.primary} 50%,
          ${colors.primary}00 100%
        );
      `,
      table: `
        border-radius: 3px;
        overflow: hidden;
        min-width: 100%;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      `,
      th: `
        background: ${colors.primary}25;
        color: ${colors.primary};
        padding: 12px;
        font-weight: 600;
		border: 1px solid #66ccff;
      `,
	  tr:`
	  border: 1px solid #66ccff;
	  `,
      td: `
        padding: 10px;
        color: #666;
		border: 1px solid #66ccff;
      `,
      pre: `
        border-radius: 5px;
        white-space: pre;
        font-size: 12px;
        position: relative;
      `
    })
  },
  none: {
    colors: {
      primary: '#555',
      secondary: '#777777',
      text: '#555555',
      border: '#dddddd'
    },
    styles: (colors) => ({
      p: `
        margin: 5px 5px;
        font-size: 15px;
        line-height: 1.75;
        letter-spacing: 0.2em;
        word-spacing: 0.1em;
      `,
      h1: `
        margin: 25px 0;
        font-size: 24px;
        text-align: center;
        font-weight: bold;
        color: ${colors.primary};
        padding: 3px 10px 1px;
        border-bottom: 2px solid ${colors.primary};
        border-top-right-radius: 3px;
        border-top-left-radius: 3px;
      `,
      h2: `
        margin: 40px 0 20px 0;	
        font-size: 20px;
        text-align: center;
        color: ${colors.primary};
        font-weight: bolder;
        padding-left: 10px;
      `,
      h3: `
        margin: 30px 0 10px 0;
        font-size: 18px;
        color: ${colors.primary};
        padding-left: 10px;
        border-left: 3px solid ${colors.primary};
      `,
      blockquote: `
        margin: 15px 0;
        font-size: 15px;
        color:${colors.secondary};
        border-left: 4px solid ${colors.border};
        padding: 0 10px;
      `,
      ul: `
        margin: 10px 0;
        color: ${colors.text};
      `,
      li: `
        margin: 5px 0;
        color: ${colors.text};
      `,
      strong: `
        font-weight: bold;
        color: ${colors.primary};
      `,
      em: `
        color: ${colors.primary};
        letter-spacing: 0.3em;
      `,
      hr: `
        height: 1px;
        padding: 0;
        border: none;
        text-align: center;
        background-image: linear-gradient(to right, rgba(248,57,41,0), ${colors.primary}, rgba(248,57,41,0));
      `,
      table: `
        border-spacing: 0;
        overflow: auto;
        min-width: 100%;
        margin: 10px 0;
        border-collapse: collapse;
      `,
      th: `
        border: 1px solid #202121;
        color: ${colors.text};
      `,
      td: `
        color: ${colors.text};
        border: 1px solid ${colors.text};
      `,
      pre: `
        border-radius: 5px;
        white-space: pre;
        font-size: 12px;
        position: relative;
      `
    })
  },  
};