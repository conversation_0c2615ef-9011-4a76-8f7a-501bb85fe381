<script setup lang="ts">
import type { MenuItem as MenuItemType } from '@/types/menu'
import { useAuthStore } from '@/stores/auth'
import MenuItem from './menuItem.vue'

defineProps<{
  title: string
  items: MenuItemType[]
}>()
const emit = defineEmits(['bind-phone', 'bind-wechat'])
const authStore = useAuthStore()

const { isPhoneLogin } = storeToRefs(authStore)

function bindPhone() {
  emit('bind-phone')
}

function bindWechat() {
  emit('bind-wechat')
}
</script>

<template>
  <view class="menu-section mt-6">
    <view class="section-title px-4 py-2 text-base font-medium text-gray-900">
      {{ title }}
    </view>
    <view class="menu-items">
      <MenuItem v-for="item in items" v-show="isPhoneLogin || (!isPhoneLogin && item.id !== 'bindWx')" :key="item.id" :item="item" @bind-phone="bindPhone" @bind-wechat="bindWechat" />
    </view>
  </view>
</template>
