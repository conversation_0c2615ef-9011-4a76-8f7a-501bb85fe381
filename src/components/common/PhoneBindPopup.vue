<script setup lang="ts">
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { ref, watch } from 'vue'

interface Props {
  visible?: boolean
  onSuccessCallback?: () => void
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
  (e: 'skip'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  onSuccessCallback: undefined,
})

const emit = defineEmits<Emits>()

const authStore = useAuthStore()
const { user, agreed } = storeToRefs(authStore)
const { checkSession, clearAuth } = authStore

// 弹窗引用
const showbindPhone = ref()

// 状态管理
const loading = ref(false)
const errorMessage = ref('')

// 监听 visible 变化，控制弹窗显示
watch(() => props.visible, (newVal) => {
  if (newVal) {
    showbindPhone.value?.open()
  }
  else {
    showbindPhone.value?.close()
  }
})

// 切换协议同意状态
function toggleAgreement() {
  agreed.value = !agreed.value
}

// 获取微信手机号
async function getPhoneNumber(e: any) {
  // 检查是否同意协议
  if (e.type === 'tap') {
    if (!agreed.value) {
      uni.showToast({
        icon: 'none',
        title: '请先同意用户协议和隐私政策',
        duration: 1000,
      })
      return
    }
    return
  }

  // 如果没有获取到code，说明用户拒绝授权
  if (e.detail.errMsg !== 'getPhoneNumber:ok') {
    uni.showToast({
      icon: 'none',
      title: '获取手机号失败，请重试',
      duration: 1000,
    })
    return
  }

  loading.value = true
  errorMessage.value = ''

  // 显示加载中提示
  uni.showLoading({
    title: '绑定中...',
    mask: true,
  })

  // 获取微信手机号码
  const { code } = e.detail
  try {
    const { data: wxData, error: bindError } = await supabase.auth.wechatBindPhone({ code })

    if (bindError) {
      if (bindError.status === 422) {
        uni.showModal({
          title: '此电话号码已存在',
          content: '请使用手机号登录,操作绑定微信',
          success: (res) => {
            clearAuth()
            if (res.confirm) {
              uni.reLaunch({
                url: '/pages/auth/login?loginMode=h5',
              })
            }
          },
        })
      }
    }

    if (!wxData.user) {
      throw new Error('绑定手机号失败')
    }
    else {
      const { data: userData, error } = await supabase.auth.updateUser({
        data: {
          full_name: wxData.user.phone,
          nickname: wxData.user.phone,
        },
      })
      authStore.InitupdatePublicUser(userData.user, true)

      uni.showToast({
        title: '手机号绑定成功',
        icon: 'success',
        duration: 1000,
      })

      // 关闭弹窗
      closePopup()
      await checkSession()

      // 触发成功事件
      emit('success')

      // 执行成功回调
      if (props.onSuccessCallback) {
        setTimeout(() => {
          props.onSuccessCallback()
        }, 1000)
      }
    }
  }
  catch (_error) {
    console.log(_error, '_error')

    // uni.showToast({
    //   title: '此电话号码的用户已经注册',
    //   icon: 'none',
    //   duration: 1000,
    // })
  }
  finally {
    loading.value = false
    uni.hideLoading()
  }
}

// 跳转到协议页面
function GoToDeclaration(page: string) {
  uni.navigateTo({
    url: `/subdeclaration/declaration/${page}`,
  })
}

// 跳过绑定手机号
function skipBindPhone() {
  closePopup()
  emit('skip')
}

// 关闭弹窗
function closePopup() {
  showbindPhone.value?.close()
  emit('update:visible', false)
}

// 暴露方法给父组件
defineExpose({
  open: () => showbindPhone.value?.open(),
  close: closePopup,
})
</script>

<template>
  <uni-popup ref="showbindPhone" type="center" :safe-area="true" :mask-click="false">
    <view class="phone-bind-popup">
      <!-- 头部图标 -->
      <view class="popup-header">
        <view class="title-section">
          <view class="main-title">
            <text class="welcome-emoji">🎉</text>
            <text class="title-text">欢迎加入</text>
          </view>
          <view class="sub-title">
            智能工作空间
          </view>
        </view>
      </view>

      <!-- 描述区域 -->
      <view class="description-section">
        <view class="security-text">
          验证手机号 · 保障账号安全
        </view>

        <view class="benefits-section">
          <view class="benefits-title">
            您的手机号将用于:
          </view>
          <view class="benefit-item">
            <view class="benefit-dot" />
            <text class="benefit-text">账号统一验证</text>
          </view>
          <view class="benefit-item">
            <view class="benefit-dot" />
            <text class="benefit-text">专属权益保障</text>
          </view>
        </view>
      </view>

      <!-- 绑定表单 -->
      <view class="bind-form-section">
        <!-- 微信手机号获取按钮 -->
        <button
          class="wechat-auth-btn" :open-type="agreed ? 'getPhoneNumber' : ''" @getphonenumber="getPhoneNumber"
          @click="getPhoneNumber"
        >
          <view class="btn-content">
            <uni-icons v-if="!loading" type="weixin" size="20" color="#fff" />
            <uni-icons v-else type="spinner-cycle" size="20" color="#fff" />
            <text class="btn-text">{{ loading ? '授权中...' : '微信授权手机号' }}</text>
          </view>
        </button>

        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-toast">
          <uni-icons type="info" size="16" color="#EF4444" />
          <text class="error-text">{{ errorMessage }}</text>
        </view>

        <!-- 协议同意区域 -->
        <view class="agreement-section">
          <view class="checkbox-container" @click="toggleAgreement">
            <view class="custom-checkbox" :class="{ checked: agreed }">
              <uni-icons v-if="agreed" type="checkmarkempty" size="14" color="#fff" />
            </view>
            <text class="agreement-text">我已阅读并同意</text>
          </view>

          <view class="agreement-links">
            <text class="agreement-link" @click="GoToDeclaration('agreement')">《用户协议》</text>
            <text class="link-separator">和</text>
            <text class="agreement-link" @click="GoToDeclaration('privacyPolicy')">《隐私政策》</text>
          </view>
        </view>
      </view>

      <!-- 底部跳过按钮 -->
      <view class="skip-section">
        <text class="skip-btn" @click="skipBindPhone">暂不绑定</text>
      </view>
    </view>
  </uni-popup>
</template>

<style scoped>
/* 弹窗容器 */
.phone-bind-popup {
  @apply relative w-80  rounded-2xl bg-white p-2 shadow-2xl py-6;
  max-height: 90vh;
  overflow-y: auto;
  animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 关闭按钮 */
.close-btn {
  @apply absolute right-4 top-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-gray-100 transition-all duration-200;
}

.close-btn:hover {
  @apply bg-gray-200;
}

.close-btn:active {
  @apply bg-gray-300;
  transform: scale(0.95);
}

/* 头部区域 */
.popup-header {
  @apply mb-6 flex flex-col items-center text-center;
}

.welcome-icon {
  @apply mb-4;
}

.icon-bg {
  @apply flex h-16 w-16 items-center justify-center rounded-full bg-blue-50;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.title-section {
  @apply space-y-1;
}

.main-title {
  @apply flex items-center justify-center space-x-2;
}

.welcome-emoji {
  @apply text-2xl;
}

.title-text {
  @apply text-xl font-bold text-gray-800;
}

.sub-title {
  @apply text-base font-medium text-gray-600;
}

/* 描述区域 */
.description-section {
  @apply mb-6 space-y-4;
}

.security-text {
  @apply text-center text-sm font-medium text-gray-500;
}

.benefits-section {
  @apply rounded-lg bg-gray-50 p-4;
}

.benefits-title {
  @apply mb-3 text-sm font-medium text-gray-700;
}

.benefit-item {
  @apply mb-2 flex items-center space-x-3 last:mb-0;
}

.benefit-dot {
  @apply h-1.5 w-1.5 rounded-full bg-blue-500;
}

.benefit-text {
  @apply text-sm text-gray-600;
}

/* 绑定表单区域 */
.bind-form-section {
  @apply mb-6 space-y-4;
}

/* 微信授权按钮 */
.wechat-auth-btn {
  @apply w-60 rounded-xl bg-gradient-to-r from-green-400 to-green-600 py-2 text-white shadow-lg transition-all duration-200 after:border-none;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.wechat-auth-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.wechat-auth-btn.btn-disabled {
  @apply bg-gray-300 from-gray-300 to-gray-400;
  box-shadow: none;
  cursor: not-allowed;
}

.btn-content {
  @apply flex items-center justify-center space-x-2;
}

.btn-text {
  @apply text-base font-medium;
}

/* 错误提示 */
.error-toast {
  @apply flex items-center space-x-2 rounded-lg bg-red-50 p-3 text-red-600;
  border: 1px solid #fee2e2;
}

.error-text {
  @apply text-sm;
}

/* 协议同意区域 */
.agreement-section {
  @apply space-y-3 mt-3;
}

.checkbox-container {
  @apply flex cursor-pointer items-center space-x-3 justify-center;
}

.custom-checkbox {
  @apply flex h-5 w-5 items-center justify-center rounded border-2 border-gray-300 transition-all duration-200;
}

.custom-checkbox.checked {
  @apply border-blue-500 bg-blue-500;
}

.agreement-text {
  @apply text-sm text-gray-600;
}

.agreement-links {
  @apply flex flex-wrap items-center justify-center space-x-1 text-sm;
}

.agreement-link {
  @apply cursor-pointer text-blue-500 transition-colors duration-200;
}

.agreement-link:active {
  @apply text-blue-700;
}

.link-separator {
  @apply text-gray-500;
}

/* 底部跳过区域 */
.skip-section {
  @apply text-center;
}

.skip-btn {
  @apply cursor-pointer text-sm text-gray-400 transition-colors duration-200;
}

.skip-btn:active {
  @apply text-gray-600;
}
</style>
