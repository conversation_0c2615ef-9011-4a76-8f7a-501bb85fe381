import type { DifyAppConfig } from '@/types/ai'
import type { AppInfo, AppParameters, ChatCache, ChatHistory, ChatMessage, FileType, InputType, Message, MessagesSuggested, SavedCompletionDisplay, UploadedFile, WorkflowNode } from '@/types/dify.type'

import { API } from '@/api/api'
import { getFileTypeByMimeType, getInputAccept } from '@/api/file-types'
import { DB_TABLES } from '@/api/tables'
import { supabase } from '@/services/supabase'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useLLMModelStore } from '@/stores/llm-model'
import { usePointsStore } from '@/stores/points'
import { useUsersStore } from '@/stores/user'
import aiConfig from '@/types/ai'
import { formatDate } from '@/utils/dateUtils'
import { decodeStream } from '@/utils/decodeMessage'
import { del, get, post, put, streamRequest, uniRequest } from '@/utils/http'
import { defineStore, storeToRefs } from 'pinia'

const userStore = useUsersStore()
const authStore = useAuthStore()
const appStore = useAppStore()
const { user } = storeToRefs(authStore)
const llmModelStore = useLLMModelStore()
const chatStore = useChatStore()
interface ExtractedInfo {
  model_provider: string
  model_name: string
  usage: any
  conversation_id: string
  message_id: string
}

interface ApiResponse<T> {
  data: T[]
  has_more: boolean
}
interface AppExtractedInfo {
  model_provider: string
  model_name: string
}
export const useDifyChatStore = defineStore('dify-chat', {
  state: () => ({
    type: 'chat',
    conversation_id: '' as string,
    isLoadingApp: true,
    appid: null as string | null,
    appInfo: null as AppInfo | null,
    appAllInfo: null,
    appParameters: null as AppParameters | null,
    userInputs: {} as Record<string, any>,
    userInputFormCache: {} as Record<string, any>,
    showUserInputForm: false,
    appExtractedInfo: null as AppExtractedInfo | null,
    showUserInputFormCancelBtn: false,
    messages: [] as Message[],
    conversationHistory: [] as ChatHistory[],
    currentConversationId: null as string | null,
    isLoadingMessage: false, // 是否正在加载会话历史消息
    isMessagesLoading: false, // 是否正在加载会话列表
    isRequesting: false,
    hasMoreHistory: false,
    lastId: null as string | null,
    firstMessageId: null as string | null,
    hasMoreMessages: false as boolean,
    isLoadingHistory: true,
    sendMmoryStatus: false,
    messageCache: new Map<string, ChatCache>(),
    uploadingFiles: ref<UploadedFile[]>([]),
    isUploading: false,
    rechargeTips: false, // 充值提示窗口状态
    generatedContent: null as string | null,
    currentMessageId: null as string | null,
    completionSavedList: [] as SavedCompletionDisplay[],
    workflowContent: null as string | null,
    workflowNodes: [] as WorkflowNode[],
    worflowRunId: null as string | null,
    messagesSuggested: [] as string[],
    responseBody: null as any,
    hasSubscription: false,
    subscriptionName: '',
    subscriptionValidDate: '',
    subscriptionCount: 0,
    subscriptionUsedCount: 0,
    availableTokens: 0,
    responseEnd: true,
  }),
  getters: {
    // 是否是一个新对话
    isNewConversation(): boolean {
      if (!this.currentConversationId) {
        // 如果有开场白，且只有一条消息，依然是新对话
        if (this.appParameters?.opening_statement) {
          return this.messages.length <= 1
        }
        // 如果没有开场白，没有消息才是新对话
        return this.messages.length === 0
      }
      return false
    },
    // 开场白
    openingStatement(): string {
      return this.appParameters?.opening_statement || ''
    },
    // 获取当前应用配置
    currentApp(): DifyAppConfig | null {
      if (!this.appid) { return null }
      return (
        Object.values(aiConfig.apps).find(app => app.id === this.appid)
        || null
      )
    },
    // 是否启用文件上传
    enableFileUpload(): boolean {
      return this.appParameters?.file_upload?.enabled || false
    },
    // 允许上传的文件类型
    allowedFileTypes(): FileType[] {
      return (
        (this.appParameters?.file_upload?.allowed_file_types as FileType[])
        || []
      )
    },
    // 允许上传的文件mime类型
    fileInputAccept(): string {
      return getInputAccept(
        this.appParameters?.file_upload?.allowed_file_types as FileType[],
      )
    },
    // 允许上传的文件最大数量
    fileInputLimit(): number {
      return this.appParameters?.file_upload?.number_limits || 1
    },
    // 获取用户配置字段
    userInputForm(): InputType[] {
      return this.appParameters?.user_input_form || []
    },
    // 是否有用户配置字段
    hasConfigInputs(): boolean {
      return !!this.appParameters?.user_input_form?.length
    },
    // 获取用户配置字段列表
    userInputFormList() {
      const list: any[] = []

      this.userInputForm.forEach((field) => {
        Object.entries(field).forEach(([key, value]) => {
          if (value && typeof value === 'object') {
            value.type = value.type || key // completion的返回值中没有type 字段
            list.push(value)
          }
        })
      })

      return list
    },
  },

  actions: {
    // 初始化dify应用
    async initDifyApp() {
      this.isLoadingApp = true
      this.clearAll()
      try {
        if (this.type === 'chat') {
          await Promise.all([
            // 获取应用信息
            this.fetchAppInfo(),
            // 加载会话列表
            this.loadConversations(),
            // 初始化对话消息
            this.initConversationMessages(),
            // 初始化应用参数
            this.initParameters(),
          ])
        }
        else if (this.type === 'completion' || this.type === 'workflow') {
          console.log('completion')

          await Promise.all([
            // 获取应用信息
            this.fetchAppInfo(),
            // 初始化应用参数
            this.initParameters(),
          ])
        }
      }
      catch (error: any) {
        console.error('Error initializing Dify app:', error)
        this.isLoadingApp = false
      }
      finally {
        this.isLoadingApp = false
      }
    },
    // 新增获取应用信息的方法
    async fetchAppInfo() {
      if (!user.value || !this.appid) { return }

      try {
        this.appAllInfo = await appStore.getApp(this.appid)

        const data = await get(
          API.dify.info,
          { user: user.value.id },
          false,
          { 'X-App-ID': this.appid },
        )

        this.appInfo = data as AppInfo
        if (this.type === 'chat' || this.type === 'completion') {
          // 解析tags数组中的JSON字符串
          // 解析tags数组中的JSON字符串到appExtractedInfo
          this.appExtractedInfo = data.tags.reduce((acc, tag) => {
            try {
              const parsed = JSON.parse(tag)
              return { ...acc, ...parsed }
            }
            catch (e) {
              console.warn('Failed to parse tag:', tag)
              return acc
            }
          }, {} as AppExtractedInfo)
        }
      }
      catch (error: any) {
        console.error('Error fetching app info:', error)

        uni.showToast({
          title: error.message || '无法获取应用信息',
          duration: 2000,
        })
      }
    },

    // 加载会话列表
    async loadConversations(loadMore = false) {
      if (this.type === 'completion') { return }

      if (!user.value) { return }

      this.isLoadingHistory = true

      try {
        const params = {
          user: user.value.id,
          limit: '10',
          ...(loadMore && this.lastId ? { last_id: this.lastId } : {}),
        }

        const data = await get<ApiResponse<ChatHistory>>(
          API.dify.conversations,
          params,
          false,
          { 'X-App-ID': this.appid || '' },

        )

        if (loadMore) {
          this.conversationHistory = [
            ...this.conversationHistory,
            ...data.data,
          ]
        }
        else {
          this.conversationHistory = data.data
        }

        this.hasMoreHistory = data.has_more
        this.lastId = data.data[data.data.length - 1]?.id || null
      }
      catch (error: any) {
        console.error('Error loading conversations:', error)
        this.isLoadingHistory = false
      }
      finally {
        this.isLoadingHistory = false
      }
    },

    // 初始化对话消息
    async initConversationMessages() {
      if (this.type === 'completion') { return }

      const conversationId = this.conversation_id
      if (conversationId) {
        await this.getMessages(conversationId)
      }
    },

    // 初始化应用参数
    async initParameters() {
      if (!user.value || !this.appid) { return }

      try {
        const parameters = await get<AppParameters>(API.dify.parameters, {
          user: user.value.id,
        }, false, {
          'X-App-ID': this.appid || '',
        })
        this.appParameters = parameters

        this.showUserInputForm = this.isNewConversation && this.hasConfigInputs

        // 如果是新对话且存在开场白，添加一条AI消息
        if (this.isNewConversation && parameters.opening_statement) {
          this.addMessage({
            id: Date.now().toString(),
            content: parameters.opening_statement,
            isBot: true,
            isOpeningStatement: true,
          })
        }
      }
      catch (error: any) {
        console.error('Error fetching app parameters:', error)

        uni.showToast({
          title: error.message || '无法获取应用参数',
          duration: 2000,
        })
      }
    },

    // 添加消息
    addMessage(message: Message) {
      this.messages.push(message)
    },

    // 清除消息
    clearMessages() {
      this.messages = []
      this.currentConversationId = null
      this.uploadingFiles = []
      this.userInputs = {}
      this.userInputFormCache = {}
      this.showUserInputForm = false
      this.showUserInputFormCancelBtn = false
    },

    // 设置应用ID
    async setAppId(appid: string) {
      if (!appid) { return }
      this.appid = appid

      const userRoleInfo = authStore.userRoleInfo || {}

      const currentUserData = authStore.user
      const uid = currentUserData?.id
      const current_company_id = userRoleInfo.current_company_id

      await this.loadSubscriptions(uid, current_company_id, this.appid)
    },

    // 设置对话类型
    async setType(type: 'chat' | 'completion' | 'workflow') {
      this.type = type
    },

    stopResponse() {
      console.log('停止请求')

      this.responseBody.abort()
    },
    // 发送对话消息（chat）
    async sendMessage(content: string) {
      if (!content.trim()) { return }

      if (!user.value) {
        setTimeout(() => {
          uni.navigateTo({
            url: 'pages/auth/login',
          })
        }, 800)
        throw new Error('用户未登录')
      }

      try {
        const canUse = await this.checkCanUse()
        if (!canUse) { return }
      }
      catch (e) {
        console.error('Error checking user points:', e)
        uni.showToast({
          title: '电力值不足，请充值后再使用',
        })
        return
      }

      const newConversation = this.isNewConversation

      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        content,
        isBot: false,
        files:
            this.uploadingFiles.length > 0
              ? [...this.uploadingFiles]
              : undefined,
      }
      this.addMessage(userMessage)
      this.isRequesting = true
      this.isLoadingMessage = true

      // 创建 AI 消息占位
      const botMessageId = (Date.now() + 1).toString()
      const botMessage: Message = {
        id: botMessageId,
        content: '',
        isBot: true,
        messageId: '',
      }
      this.addMessage(botMessage)

      // 准备文件数据
      const files = this.uploadingFiles.map((file) => {
        const fileType = getFileTypeByMimeType(file.type)

        return {
          type: fileType,
          transfer_method: file.url ? 'remote_url' : 'local_file',
          ...(file.url
            ? { url: file.url }
            : { upload_file_id: file.upload_file_id }),
        }
      })

      // 准备请求数据
      const requestData = {
        query: content,
        user: user.value.id,
        response_mode: 'streaming',
        conversation_id: this.currentConversationId || undefined,
        auto_generate_name: true,
        files: files.length > 0 ? files : undefined,
        inputs: this.sendMmoryStatus ? this.userInputs : {},
      }

      // 准备请求头
      const requestHeaders = {
        'Content-Type': 'application/json',
        'X-App-ID': this.appid || '',
      }

      // 使用流式请求方法

      // console.log(API.dify, 'API.dify.chatMessages')
      let accumulatedContent = '' // 用于存储不完整的数据
      let buffer = ''
      let sourceResponse = ''

      try {
        this.responseBody = await streamRequest(
          API.dify.chatMessages,
          'POST',
          requestHeaders,
          requestData,
          (chunk) => {
            console.log(chunk, 'chunk')
          },
          async (_fullData) => {
            // 流式请求完成后的处理
            this.responseEnd = true
            this.isLoadingMessage = false
            this.isRequesting = false
            await this.handleResponse('chat', `[${sourceResponse}]`)
            // 在消息发送成功后更新缓存

            if (buffer.trim()) {
              const remainingMatches = buffer.match(/data: (\{.*?\})\n\n/gs)

              if (remainingMatches) {
                for (const match of remainingMatches) {
                  try {
                    const json = JSON.parse(match.replace('data: ', ''))
                    const messageIndex = this.messages.findIndex(
                      m => m.id === botMessageId,
                    )

                    if (json.event === 'message') {
                      if (json.conversation_id) {
                        console.log('更新currentConversationId前', this.currentConversationId, json.conversation_id)
                        this.currentConversationId = json.conversation_id

                        console.log('更新currentConversationId后', this.currentConversationId, json.conversation_id)
                      }

                      accumulatedContent += json.answer || ''
                      if (messageIndex !== -1) {
                        this.messages[messageIndex].content = accumulatedContent
                      }
                      // 如果消息没有messageId，则设置messageId
                      if (!this.messages[messageIndex].messageId) {
                        this.messages[messageIndex].messageId = json.message_id
                      }
                    }
                    else if (json.event === 'message_end') {
                      // 打印完整的json
                      // await this.handleResponse('chat', `[${sourceResponse}]`) // 收到message_end 信息后，需要提交整体信息
                      console.log('message_end_message_end', json.content)

                      if (json.conversation_id) {
                        console.log('更新currentConversationId前', this.currentConversationId, json.conversation_id)
                        this.currentConversationId = json.conversation_id

                        console.log('更新currentConversationId后', this.currentConversationId, json.conversation_id)
                      }
                      if (json.files && messageIndex !== -1) {
                        this.messages[messageIndex].files = json.files
                      }
                    }
                    else if (json.event === 'workflow_started') {
                      this.messages[messageIndex].workflows = [json]
                    }
                    else if (json.event === 'error') {
                      throw new Error(json.message || 'Stream error')
                    }
                    else if (
                      !json.event.data?.iteration_id
                      && json.event !== 'iteration_next'
                    ) {
                      this.messages[messageIndex].workflows?.push(json)
                    }
                  }
                  catch (e) {
                    console.warn('Could not parse remaining data:', match, e)
                  }
                }
              }
            // return
            }
            console.log(this.currentConversationId, 'this.currentConversationIdthis.currentConversationId')

            if (this.currentConversationId) {
              this.messageCache.set(this.currentConversationId, {
                messages: this.messages,
                firstMessageId: this.firstMessageId,
                hasMoreMessages: this.hasMoreMessages,
              })
            }
            // 清除上传的文件
            this.clearUploadedFiles()
            // 如果是一个新对话，重新请求文件列表
            if (newConversation) {
              this.loadConversations()
            }
          },
        )
      }
      catch (error: any) {
        console.error('Error sending message:', error)
        this.isLoadingMessage = false
        this.isRequesting = false
        const lastMessage = this.messages[this.messages.length - 1]
        if (lastMessage?.isBot && lastMessage?.content === '') {
          this.messages.pop()
        }

        // 添加错误消息
        this.addMessage({
          id: Date.now().toString(),
          content: error.message || '抱歉，发生了错误，请稍后重试。',
          isBot: true,
          error: true,
        })

        // 显示错误提示
        uni.showToast({

          title: error.message || '消息发送失败，请稍后重试',
          duration: 2000,
        })

        throw error // 继续抛出错误以便上层处理
      }
      this.responseEnd = false
      // 流式请求回调信息处理  start requestTask

      this.responseBody.onChunkReceived(async (res: any) => {
        // 处理sse数据
        if (this.isRequesting) {
          this.isRequesting = false
        }
        // 将二进制数据转换为文本
        const uint8Array = new Uint8Array(res.data)
        const text = String.fromCharCode.apply(null, Array.from(uint8Array))

        // 如果还有剩余数据，确保处理完

        buffer += decodeURIComponent(encodeURI(text).replace('/\"/g', ''))
        // 尝试从buffer中提取完整的消息
        const messages = []
        let lastIndex = 0
        const pattern = /data: (\{.*?\})\n\n/gs
        let match

        while ((match = pattern.exec(buffer)) !== null) {
          try {
            if (sourceResponse != '') {
              sourceResponse += ','
            }
            sourceResponse += match[1]
            const json = JSON.parse(match[1])
            messages.push(json)
            lastIndex = match.index + match[0].length
          }
          catch (e) {
            console.warn('JSON parse error, waiting for more data')
            break
          }
        }

        // 只保留未处理完的数据
        if (lastIndex > 0) {
          buffer = buffer.slice(lastIndex)
        }

        // 处理完整的消息
        for (const msg of messages) {
          const messageIndex = this.messages.findIndex(
            m => m.id === botMessageId,
          )
          if (msg.event === 'message') {
            // 创建 AI 消息占位 += msg.answer || ''
            if (messageIndex !== -1) {
              this.messages[messageIndex].content = accumulatedContent
            }
            if (!this.messages[messageIndex].messageId) {
              this.messages[messageIndex].messageId = msg.message_id
            }
          }
          else if (msg.event === 'message_end') {
            console.log('message_end_message_end', msg.content)

            // 打印完整的json
            // await this.handleResponse('chat', `[${sourceResponse}]`) // 收到message_end 信息后，需要提交整体信息
            if (msg.conversation_id) {
              this.currentConversationId = msg.conversation_id
            }
            if (msg.files && messageIndex !== -1) {
              this.messages[messageIndex].files = msg.files
            }
          }
          else if (msg.event === 'workflow_started') {
            this.messages[messageIndex].workflows = [msg]
          }
          else if (msg.event === 'error') {
            throw new Error(msg.message || 'Stream error')
          }
          else if (
            !msg.event.data?.iteration_id
            && msg.event !== 'iteration_next'
          ) {
            this.messages[messageIndex].workflows?.push(msg)
          }
        }
      }) // end requestTask
    },

    // 开始新对话
    startNewConversation() {
      this.clearMessages()
      setTimeout(() => {
        this.showUserInputForm = this.hasConfigInputs
        // 如果存在开场白，添加一条AI消息
        if (this.appParameters?.opening_statement) {
          this.addMessage({
            id: Date.now().toString(),
            content: this.appParameters.opening_statement,
            isBot: true,
          })
        }
      })
    },

    // 获取会话
    async getMessages(conversation_id: string) {
      this.currentConversationId = conversation_id
      this.isMessagesLoading = true

      try {
        // 检查缓存
        const cachedData = this.messageCache.get(conversation_id)
        if (cachedData) {
          // 使用缓存数据
          this.messages = cachedData.messages
          this.firstMessageId = cachedData.firstMessageId
          this.hasMoreMessages = cachedData.hasMoreMessages
          this.isMessagesLoading = false
          return
        }

        // 如果没有缓存，从服务器加载
        this.messages = []
        this.firstMessageId = null
        await this.fetchMessages()
      }
      catch (error: any) {
        uni.showToast({

          title: '加载失败',

          duration: 2000,
        })
      }
      finally {
        this.isMessagesLoading = false
      }
    },

    // 加载会话历史消息
    async fetchMessages(loadMore = false) {
      if (!user.value || !this.currentConversationId) { return }

      try {
        // 如果加载更多，不使用缓存
        if (!loadMore) {
          const cachedData = this.messageCache.get(this.currentConversationId)
          if (cachedData) {
            this.messages = cachedData.messages
            this.firstMessageId = cachedData.firstMessageId
            this.hasMoreMessages = cachedData.hasMoreMessages
            return
          }
        }

        const params = {
          conversation_id: this.currentConversationId,
          user: user.value.id,
          limit: '20',
          ...(loadMore && this.firstMessageId
            ? { first_id: this.firstMessageId }
            : {}),
        }

        const data = await get<ApiResponse<ChatMessage>>(
          API.dify.messages,
          params,
          false,
          { 'X-App-ID': this.appid || '' },
        )

        // 转换消息
        const newMessages: Message[] = []
        data.data.forEach((msg: ChatMessage) => {
          if (msg.query) {
            newMessages.push({
              id: `${msg.id}-query`,
              content: msg.query,
              isBot: false,
              files: msg.message_files
                ?.filter((file) => {
                  return file.belongs_to === 'user'
                })
                ?.map(file => ({
                  id: file.id,
                  name: file.filename,
                  size: file.size,
                  mime_type: file.mime_type,
                  type: file.type,
                  url: file.url,
                })),
            })
          }
          if (msg.answer) {
            newMessages.push({
              id: `${msg.id}-answer`,
              content: msg.answer,
              isBot: true,
              feedback: msg.feedback as Message['feedback'],
              files: msg.message_files
                ?.filter((file) => {
                  return file.belongs_to === 'assistant'
                })
                ?.map(file => ({
                  id: file.id,
                  name: file.filename,
                  size: file.size,
                  mime_type: file.mime_type,
                  type: file.type,
                  url: file.url,
                })),
            })
          }
        })

        // 更新消息列表
        if (loadMore) {
          this.messages = [...newMessages, ...this.messages]
        }
        else {
          this.messages = newMessages
        }

        // 更新分页信息
        this.hasMoreMessages = data.has_more
        if (data.data.length > 0) {
          this.firstMessageId = data.data[0].id
        }

        // 如果还没有更多消息，并且有开场白，添加开场白
        if (!this.hasMoreMessages && this.openingStatement) {
          if (this.messages.length && this.messages[0].isOpeningStatement) {
            return
          }
          this.messages.unshift({
            id: Date.now().toString(),
            content: this.openingStatement,
            isBot: true,
            isOpeningStatement: true,
          })
        }

        // 更新缓存
        if (!loadMore) {
          this.messageCache.set(this.currentConversationId, {
            messages: this.messages,
            firstMessageId: this.firstMessageId,
            hasMoreMessages: this.hasMoreMessages,
          })
        }
      }
      catch (error: any) {
        console.error('Error loading messages:', error)
        throw error
      }
    },

    // 重命名会话
    async renameConversation(conversationId: string, newName: string) {
      try {
        if (!user.value) { return }

        await post(API.dify.renameConversation(conversationId), {
          name: newName,
          user: user.value.id,
          auto_generate: false,
        }, { 'X-App-ID': this.appid || '' })

        // 更新本地状态
        const index = this.conversationHistory.findIndex(
          conversation => conversation.id === conversationId,
        )
        if (index !== -1) {
          this.conversationHistory[index].name = newName
        }
      }
      catch (error: any) {
        console.error('Error renaming conversation:', error)
        throw error
      }
    },

    // 删除会话
    async deleteConversation(conversationId: string) {
      try {
        if (!user.value) { return }

        await del(API.dify.deleteConversation(conversationId), { user: user.value.id }, { 'X-App-ID': this.appid || '' })

        // 更新本地状态
        this.conversationHistory = this.conversationHistory.filter(
          conversation => conversation.id !== conversationId,
        )

        // 如果删除是当前对话，清空当前对话并重置 URL
        if (this.currentConversationId === conversationId) {
          this.startNewConversation()
        }

        // 删除缓存
        this.messageCache.delete(conversationId)
      }
      catch (error: any) {
        console.error('Error deleting conversation:', error)
        throw error
      }
    },

    // 清除缓存的方法
    clearCache(conversationId?: string) {
      if (conversationId) {
        this.messageCache.delete(conversationId)
      }
      else {
        this.messageCache.clear()
      }
    },

    // 清除所有
    clearAll() {
      this.clearMessages()
      this.appInfo = null
      this.appParameters = null
      this.isRequesting = false
      this.rechargeTips = false
      this.isLoadingApp = true
      this.isLoadingMessage = false
      this.isMessagesLoading = false
      this.conversationHistory = []
      this.clearCache()
    },

    // 上传文件到dify
    async uploadFileToDify(file: File) {
      if (!user.value) {
        setTimeout(() => {
          uni.navigateTo({
            url: 'pages/auth/login',
          })
        }, 1000)
        throw new Error('用户未登录')
      }

      const formData = new FormData()
      formData.append('file', file)
      formData.append('user', user.value.id)

      const response = await post<{ id: string }>(API.dify.fileUpload, formData, { 'X-App-ID': this.appid || '' })

      return response
    },

    // 上传文件
    async uploadFile(file: File) {
      if (!user.value) {
        setTimeout(() => {
          uni.navigateTo({
            url: 'pages/auth/login',
          })
        }, 1000)
        throw new Error('用户未登录')
      }

      this.isUploading = true

      try {
        let uploadedFile: UploadedFile

        // 判断是否为图片
        if (file.type.startsWith('image/')) {
          // 图片使用 Supabase 上传
          const fileExt = file.name.split('.').pop()
          const fileName = `${crypto.randomUUID()}.${fileExt}`
          const filePath = `${user.value.id}/${fileName}`
          const BUCKET_NAME = 'dify-files'

          const { data, error } = await supabase.storage
            .from(BUCKET_NAME)
            .upload(filePath, file, {
              cacheControl: '3600',
              upsert: false,
            })

          if (error) { throw error }

          const {
            data: { publicUrl },
          } = supabase.storage.from(BUCKET_NAME).getPublicUrl(filePath)

          uploadedFile = {
            id: crypto.randomUUID(),
            name: file.name,
            size: file.size,
            type: file.type,
            url: publicUrl,
            path: filePath,
          }
        }
        else {
          const response = await this.uploadFileToDify(file)

          uploadedFile = {
            id: crypto.randomUUID(),
            name: file.name,
            size: file.size,
            type: file.type,
            upload_file_id: response.id,
          }
        }

        this.uploadingFiles.push(uploadedFile)
        return uploadedFile
      }
      catch (error: any) {
        uni.showToast({

          title: error.message || '文件上传失败',
          duration: 2000,
        })
        throw error
      }
      finally {
        this.isUploading = false
      }
    },

    // 清除上传文件
    clearUploadedFiles() {
      this.uploadingFiles = []
    },

    // 校验输入
    validateUserInputs() {
      let valid = true
      const { userInputs, userInputFormList } = this

      for (const field of userInputFormList) {
        const value = userInputs[field.variable]
        if (!value && field.required) {
          uni.showToast({

            title: `${field.label}必填！`,
            duration: 2000,
          })
          valid = false
          break
        }

        if (field.type === 'text-input' || field.type === 'paragraph') {
          if (value && field.max_length && value?.length > field.max_length) {
            uni.showToast({

              title: `${field.label}不能超过${field.max_length}字！`,
              duration: 2000,
            })
            valid = false
            break
          }
        }
      }

      return valid
    },

    // 开始对话
    async startConversationByInputs() {
      if (!this.validateUserInputs()) { return }

      this.showUserInputForm = false
    },

    // 开始文本生成（completion）
    async startTextGeneration() {
      if (!this.validateUserInputs()) { return }

      try {
        await this.requestTextGeneration()
      }
      catch (error: any) {
        console.error('Error generating text:', error)

        uni.showToast({
          title: error.message || '内容生成失败，请稍后重试',
          duration: 2000,
        })
        this.generatedContent = null
      }
      finally {
        // fd
      }
    },

    // 开始工作流（workflow）
    async startWorkflow() {
      if (!this.validateUserInputs()) { return }

      try {
        await this.requestWorkflow()
      }
      catch (error: any) {
        console.error('Error generating text:', error)

        uni.showToast({
          title: error.message || '执行失败，请稍后重试',
          duration: 2000,
        })
        this.generatedContent = null
      }
    },

    // 请求文本生成
    async requestTextGeneration() {
      this.isLoadingMessage = true
      if (!user.value) {
        setTimeout(() => {
          uni.navigateTo({
            url: 'pages/auth/login',
          })
        }, 2)
        throw new Error('用户未登录')
      }
      const currentUserData = await userStore.getCurrentUser()
      const uid = currentUserData?.uid
      // const current_company_id = authStore?.userRoleInfo.current_company_id

      try {
        const canUse = await this.checkCanUse()
        if (!canUse) { return }
      }
      catch (e) {
        console.error('Error checking user points:', e)
        uni.showToast({
          icon: 'none',
          title: '电力值不足，请充值后再使用',
        })
        return
      }
      let accumulatedContent = ''
      let buffer = '' // 用于存储不完整的数据
      let sourceResponse = ''
      this.responseBody = await streamRequest(
        API.dify.completionMessages,
        'POST',
        {
          'Content-Type': 'application/json',
          'X-App-ID': this.appid || '',
        },
        JSON.stringify({
          inputs: this.userInputs,
          user: user.value.id,
          response_mode: 'streaming',
        }),
        (res) => {
          console.log(res, 'cccchunk')
        },
        // 完成回调函数
        // 流式请求完成后的处理已在onComplete回调中完成
        async (_fullData) => {
          this.isLoadingMessage = false
          this.isRequesting = false

          if (buffer.trim()) {
            const remainingMatches = buffer.match(/data: (\{.*?\})\n\n/gs)
            if (remainingMatches) {
              for (const match of remainingMatches) {
                try {
                  const json = JSON.parse(match.replace('data: ', ''))
                  if (json.event === 'message') {
                    accumulatedContent += json.answer || ''
                    this.generatedContent = accumulatedContent
                    if (json.message_id) {
                      this.currentMessageId = json.message_id
                    }
                  }
                  else if (json.event === 'message_end') {
                    // 打印完整的json
                    await this.handleResponse('textGeneration', `[${sourceResponse}]`)
                  }
                  else if (json.event === 'error') {
                    throw new Error(json.message || 'Stream error')
                  }
                }
                catch (e) {
                  console.warn('Could not parse remaining data:', match)
                }
              }
            }
          }

          console.log(_fullData, '_fullData_fullData_fullData_fullData')
        },

      )

      this.responseBody.onChunkReceived(async (res) => { // 处理sse数据
        if (this.isRequesting) {
          this.isRequesting = false
        }

        // 将二进制数据转换为文本
        const uint8Array = new Uint8Array(res.data)
        const text = String.fromCharCode.apply(null, Array.from(uint8Array))
        buffer += decodeURIComponent(encodeURI(text).replace('/\"/g', ''))

        if (!buffer) {
          throw new Error('No response body')
        }

        // 将新的chunk添加到buffer中

        // 尝试从buffer中提取完整的消息
        const messages = []
        let lastIndex = 0
        const pattern = /data: (\{.*?\})\n\n/gs
        let match = pattern.exec(buffer)

        if (match !== null) {
          try {
            if (sourceResponse !== '') { sourceResponse += ',' }
            sourceResponse += match[1]
            const json = JSON.parse(match[1])
            messages.push(json)
            lastIndex = match.index + match[0].length
          }
          catch (e) {
            console.warn('JSON parse error, waiting for more data')
          }
        }

        // 只保留未处理完的数据
        if (lastIndex > 0) {
          buffer = buffer.slice(lastIndex)
        }

        // 处理完整的消息
        for (const msg of messages) {
          if (msg.event === 'message') {
            accumulatedContent += msg.answer || ''
            this.generatedContent = accumulatedContent
            if (msg.message_id) {
              this.currentMessageId = msg.message_id
            }
          }
          else if (msg.event === 'message_end') {
            // 打印完整的json
            await this.handleResponse('textGeneration', `[${sourceResponse}]`)
          }
          else if (msg.event === 'error') {
            throw new Error(msg.message || 'Stream error')
          }
        }
      })
    },

    parseChatResponse(jsonString: string): ExtractedInfo[] {
      try {
        const jsonData = JSON.parse(jsonString)
        if (!Array.isArray(jsonData)) {
          throw new TypeError('解析后的 JSON 数据不是数组')
        }

        // 查找 message_end 事件
        const messageEndEvent = jsonData.find(
          item => item.event === 'message_end',
        )
        if (!messageEndEvent) {
          return []
        }

        return [
          {
            conversation_id: messageEndEvent.task_id,
            message_id: messageEndEvent.message_id,
            model_provider: this.appExtractedInfo?.model_provider || '',
            model_name: this.appExtractedInfo?.model_name || '',
            usage: messageEndEvent.metadata?.usage || null,
          },
        ]
      }
      catch (error) {
        if (error instanceof Error) {
          console.error('解析 JSON 字符串时出错:', error.message)
        }
        else {
          console.error('解析 JSON 字符串时出现未知错误:', error)
        }
        return []
      }
    },
    parseTextGenerationResponse(jsonString: string): ExtractedInfo[] {
      return this.parseChatResponse(jsonString)
    },
    async handleResponse(
      llmType: 'chat' | 'textGeneration' | 'workflow',
      response: any,
    ) {
      let extractedInfo: ExtractedInfo[] = []
      console.log('handleResponsehandleResponsehandleResponse', response)

      if (llmType === 'chat') {
        extractedInfo = this.parseWorkflowResponse(response)
        if (extractedInfo.length === 0) {
          extractedInfo = this.parseChatResponse(response)
          if (extractedInfo.length > 0) {
            // 在聊天完成后加载建议问题
            this.loadMessagesSuggested(extractedInfo[0].message_id)
          }
        }
      }
      else if (llmType === 'textGeneration') {
        extractedInfo = this.parseTextGenerationResponse(response)
      }
      else if (llmType === 'workflow') {
        extractedInfo = this.parseWorkflowResponse(response)
      }
      this.handleTokenDeduction(extractedInfo)
    },

    parseWorkflowResponse(jsonString: string): ExtractedInfo[] {
      try {
        const jsonData = JSON.parse(jsonString)
        if (!Array.isArray(jsonData)) {
          throw new TypeError('解析后的 JSON 数据不是数组')
        }

        const filteredData = jsonData.filter(item => item.event === 'node_finished' && item.data.node_type === 'llm')
        const extractedData = filteredData.map((item) => {
          const { model_mode, model_provider, model_name }
            = item.data.process_data || {}
          const usage = item.data.outputs?.usage

          return {
            conversation_id: item.task_id,
            message_id: item.workflow_run_id,
            model_mode,
            model_provider,
            model_name,
            usage,
          }
        })

        return extractedData
      }
      catch (error) {
        if (error instanceof Error) {
          console.error('解析 JSON 字符串时出错:', error.message)
        }
        else {
          console.error('解析 JSON 字符串时出现未知错误:', error)
        }
        return []
      }
    },
    // 请求工作流
    async requestWorkflow() {
      this.isLoadingMessage = true
      if (!user.value) {
        setTimeout(() => {
          uni.navigateTo({
            url: 'pages/auth/login',
          })
        }, 1000)
        throw new Error('用户未登录')
      }
      const uid = user.value.id
      const current_company_id = authStore?.userRoleInfo.current_company_id

      try {
        const canUse = await this.checkCanUse()
        if (!canUse) { return }
      }
      catch (e) {
        console.error('Error checking user points:', e)
        uni.showToast({
          title: '电力值不足，请充值后再使用',
        })
        return
      }

      const getValidOutput = (outputs: any) => {
        if (!outputs) { return null }

        // 过滤掉系统字段
        const validKeys = Object.keys(outputs).filter(
          key => !key.startsWith('sys.'),
        )

        // 如果只有一个非系统字段，直接返回它
        if (validKeys.length === 1) {
          return outputs[validKeys[0]]
        }

        // 优先查找包含实际数据的字段
        for (const key of validKeys) {
          const value = outputs[key]
          // 检查是否是JSON字符串
          if (typeof value === 'string') {
            try {
              JSON.parse(value)
              return value
            }
            catch (e) {
              // 不是JSON，继续检查
            }
          }
          // 检查是否是对象或非空内容
          if (
            value
            && (typeof value === 'object' || typeof value === 'string')
          ) {
            return value
          }
        }

        // 如果没找到有效数据，返回整个outputs对象
        return validKeys.length > 0 ? outputs[validKeys[0]] : null
      }

      // 清空之前的数据
      this.workflowContent = '' // 初始化为空字符串而非 null
      this.workflowNodes = []

      // 准备请求数据
      const requestData = {
        inputs: this.userInputs,
        user: user.value.id,
        response_mode: 'streaming',
      }

      // 准备请求头
      const requestHeaders = {
        'Content-Type': 'application/json',
        'X-App-ID': this.appid || '',
      }

      let buffer = '' // 用于存储不完整的数据
      let sourceResponse = ''
      // 使用流式请求方法
      this.responseBody = await streamRequest(
        API.dify.workflowRun,
        'POST',
        requestHeaders,
        requestData,
        // 数据块回调函数
        (chunk) => {
          console.log(chunk, 'chunk')
        },
        async (_fullData) => {
          // 完成回调函数

          console.log(_fullData, '_fullData_fullData_fullData_fullData')
          this.isLoadingMessage = false
          this.isRequesting = false

          if (buffer.trim()) {
            const remainingMatches = buffer.match(/data: (\{.*?\})\n\n/gs)
            if (remainingMatches) {
              for (const match of remainingMatches) {
                try {
                  const json = JSON.parse(match.replace('data: ', ''))
                  if (json.event === 'workflow_finished') {
                    if (json.data.total_tokens) {
                      // 打印完整的json
                      await this.handleResponse('workflow', `[${sourceResponse}]`)
                    }
                    // 如果有最终结果，使用它替换流式内容
                    const finalOutput = getValidOutput(json.data.outputs)
                    if (finalOutput) {
                      this.workflowContent = finalOutput
                    }
                    this.worflowRunId = json.workflow_run_id
                  }
                  else if (json.event === 'text_chunk') {
                    // 直接更新 workflowContent
                    this.workflowContent
                      = (this.workflowContent || '') + (json.data.text || '')
                  }
                  else if (json.event === 'node_finished') {
                    // 为所有节点添加完成事件，包括迭代节点
                    this.workflowNodes.push(json)
                  }
                  else if (json.event === 'iteration_completed') {
                    // 处理迭代完成事件 - 这会标记迭代节点为完成状态
                    this.workflowNodes.push(json)
                  }
                  else if (json.event === 'iteration_next') {
                    // 处理迭代下一步事件 - 这只更新当前迭代的状态
                    // 查找是否已有相同节点ID的节点
                    const existingIndex = this.workflowNodes.findIndex(
                      node =>
                        node.data?.node_id === json.data?.node_id
                        && (node.event === 'node_started'
                          || node.event === 'iteration_next'),
                    )

                    if (existingIndex !== -1) {
                      // 更新现有节点的迭代信息
                      this.workflowNodes[existingIndex] = json
                    }
                    else {
                      // 添加新的迭代节点
                      this.workflowNodes.push(json)
                    }
                  }
                  else if (!json.data?.iteration_id) {
                    // 处理非迭代节点
                    this.workflowNodes.push(json)
                  }
                  else {
                    // 处理带有iteration_id的节点（迭代的子节点）
                    // 确保它们作为正常节点添加，但保留其迭代信息
                    this.workflowNodes.push(json)
                  }
                }
                catch (e) {
                  console.warn('Could not parse remaining data:', match)
                }
              }
            }
          }
        },

      )

      this.responseBody.onChunkReceived(async (res: any) => { // 处理sse数据
        // 将二进制数据转换为文本
        const uint8Array = new Uint8Array(res.data)
        const text = String.fromCharCode.apply(null, Array.from(uint8Array))
        buffer += decodeURIComponent(encodeURI(text).replace('/\"/g', ''))

        // 尝试从buffer中提取完整的消息
        const messages = []
        let lastIndex = 0
        const pattern = /data: (\{.*?\})\n\n/gs
        let match = ''

        if ((match = pattern.exec(buffer)) !== null) {
          try {
            if (sourceResponse !== '') {
              sourceResponse += ','
            }
            sourceResponse += match[1]
            const json = JSON.parse(match[1])
            messages.push(json)
            lastIndex = match.index + match[0].length
          }
          catch (e) {
            console.warn('JSON parse error, waiting for more data')
          }
        }

        // 只保留未处理完的数据
        if (lastIndex > 0) {
          buffer = buffer.slice(lastIndex)
        }

        // 处理完整的消息
        for (const msg of messages) {
          if (msg.event === 'workflow_finished') {
            if (msg.data.total_tokens) {
              await this.handleResponse('workflow', `[${sourceResponse}]`)
            }
            // 如果有最终结果，使用它替换流式内容
            const finalOutput = getValidOutput(msg.data.outputs)
            if (finalOutput) {
              this.workflowContent = finalOutput
            }
            this.worflowRunId = msg.workflow_run_id
          }
          else if (msg.event === 'text_chunk') {
            // 直接更新 workflowContent
            this.workflowContent
              = (this.workflowContent || '') + (msg.data.text || '')
          }
          else if (msg.event === 'node_finished') {
            // 为所有节点添加完成事件，包括迭代节点
            this.workflowNodes.push(msg)
          }
          else if (msg.event === 'iteration_completed') {
            // 处理迭代完成事件 - 这会标记迭代节点为完成状态
            this.workflowNodes.push(msg)
          }
          else if (msg.event === 'iteration_next') {
            // 处理迭代下一步事件 - 这只更新当前迭代的状态
            // 查找是否已有相同节点ID的节点
            const existingIndex = this.workflowNodes.findIndex(
              node =>
                node.data?.node_id === msg.data?.node_id
                && (node.event === 'node_started'
                  || node.event === 'iteration_next'),
            )

            if (existingIndex !== -1) {
              // 更新现有节点的迭代信息
              this.workflowNodes[existingIndex] = msg
            }
            else {
              // 添加新的迭代节点
              this.workflowNodes.push(msg)
            }
          }
          else if (!msg.data?.iteration_id) {
            // 处理非迭代节点
            this.workflowNodes.push(msg)
          }
          else {
            // 处理带有iteration_id的节点（迭代的子节点）
            // 确保它们作为正常节点添加，但保留其迭代信息
            this.workflowNodes.push(msg)
          }
        }
      })
    },

    // 保存生成内容
    async saveCompletionContent() {
      if (this.type === 'completion') {
        if (!user.value || !this.generatedContent || !this.currentMessageId) {
          uni.showToast({
            icon: 'none',
            title: '无法保存内容，缺少必要信息',
            duration: 2000,
          })
          return
        }
      }
      else if (this.type === 'workflow') {
        if (!user.value || !this.workflowContent || !this.worflowRunId) {
          uni.showToast({
            icon: 'none',
            title: '无法保存内容，缺少必要信息',
            duration: 2000,
          })
          return
        }
      }

      if (!this.appid) {
        uni.showToast({
          icon: 'none',
          title: '无法保存内容，缺少必要信息',
          duration: 2000,
        })
        return
      }

      const id
        = this.type === 'completion' ? this.currentMessageId : this.worflowRunId
      const content
        = this.type === 'completion'
          ? this.generatedContent
          : this.workflowContent

      // 检查是否已经保存过
      const existingSaved = this.completionSavedList.find(
        item => item.id === id,
      )

      if (existingSaved) {
        uni.showToast({
          icon: 'none',
          title: '该内容已经保存过了',
          duration: 2000,
        })
        return
      }

      try {
        const { data, error } = await supabase
          .from(DB_TABLES.DIFY_TEXT_COMPLETION_SAVED)
          .insert({
            id,
            query: JSON.stringify(this.userInputs),
            answer: content,
            user_id: user.value?.id,
            app_id: this.appid,
          })
          .select()
          .single()

        if (error) { throw error }

        // 添加到列表
        this.completionSavedList.unshift({
          id: data.id,
          content: data.answer,
          created_at: data.created_at,
        })

        uni.showToast({
          icon: 'none',
          title: '内容已保存到我的收藏',
          duration: 2000,
        })
      }
      catch (error: any) {
        console.error('Error saving completion:', error)
        uni.showToast({
          icon: 'none',
          title: error.message || '无法保存内容',
          duration: 2000,
        })
      }
    },

    // 获取保存的内容列表
    async getCompletionSaved() {
      console.log(user.value, 'user.value', this.appid)

      if (!user.value || !this.appid) { return }

      try {
        const { data, error } = await supabase
          .from(DB_TABLES.DIFY_TEXT_COMPLETION_SAVED)
          .select('*')
          .eq('user_id', user.value.id)
          .eq('app_id', this.appid)
          .order('created_at', { ascending: false })

        if (error) { throw error }

        this.completionSavedList = data.map(item => ({
          id: item.id,
          content: item.answer,
          created_at: item.created_at,
        }))
      }
      catch (error: any) {
        console.error('Error fetching saved completions:', error)

        uni.showToast({

          title: error.message || '无法获取保存的内容',
          duration: 2000,
        })
      }
    },

    // 删除保存的内容
    async deleteCompletionSaved(id: string) {
      if (!user.value) { return }

      try {
        const { error } = await supabase
          .from(DB_TABLES.DIFY_TEXT_COMPLETION_SAVED)
          .delete()
          .eq('id', id)

        if (error) { throw error }

        // 从列表中移除
        this.completionSavedList = this.completionSavedList.filter(
          item => item.id !== id,
        )

        uni.showToast({
          title: '内容已删除',
          duration: 2000,
        })
      }
      catch (error: any) {
        console.error('Error deleting saved completion:', error)
        uni.showToast({

          title: error.message || '无法删除内容',
          duration: 2000,
        })
      }
    },

    async loadSubscriptions(uid: string, companyId: string, appId: string) {
      const appStore = useAppStore()
      const chatStore = useChatStore()

      const pointsStore = usePointsStore()
      let subscription = await chatStore.getAvailableSubscription(
        uid,
        companyId,
        appId,
      )
      if (subscription.length != 0) {
        subscription = subscription[0]
        this.hasSubscription = true
      }
      else {
        this.hasSubscription = false
      }
      let points = await pointsStore.getRemainingPoints(companyId)
      console.log('subscription', subscription.subscription_count)
      console.log('points', points)

      if (this.hasSubscription) {
        if (subscription.package_type == 'PerTime') {
          this.subscriptionName = '按次'
          this.subscriptionValidDate = formatDate(subscription.expiry_time)
          this.subscriptionCount = subscription.subscription_count
          this.subscriptionUsedCount = subscription.used_count
        }
        else if (subscription.package_type == 'PerDay') {
          this.subscriptionName = '按日'
          this.subscriptionValidDate = formatDate(subscription.expiry_time)
          this.subscriptionCount = subscription.max_usage_per_day

          // 判断最后使用时间是否为今天
          const today = new Date().toISOString().split('T')[0]
          const lastUsedDate = new Date(subscription.last_used_time)
            .toISOString()
            .split('T')[0]
          this.subscriptionUsedCount
            = today === lastUsedDate ? subscription.last_day_used_count : 0
        }
      }

      let app = await appStore.getApp(appId)
      if (app) {
        this.isFree = app.is_free
      }

      this.availableTokens = points.remaining_points
        ? points.remaining_points
        : 0
    },

    // 处理token扣减
    async handleTokenDeduction(extractedInfo: ExtractedInfo[]) {
      /* const { user } = useUser(); */

      const chatStore = useChatStore()
      const llmModelStore = useLLMModelStore()

      const current_company_id = authStore?.userRoleInfo.current_company_id
      const currentUserData = authStore.user
      const uid = currentUserData?.id

      // try {
      //   for (const info of extractedInfo) {
      //     const llmModel = await llmModelStore.getLLMModelByNameAndProvider(
      //       info.model_name,
      //       info.model_provider,
      //     )

      //     await chatStore.calculateConsumption(
      //       uid,
      //       llmModel.id,
      //       current_company_id,
      //       this.appid || '',
      //       info.usage.prompt_tokens,
      //       info.usage.completion_tokens,
      //       info,
      //       info.conversation_id,
      //       info.message_id,
      //     )
      //   }
      // }
      // catch (error: any) {
      //   uni.showToast({
      //     title: error.message || '无法计算消耗',
      //     duration: 2000,
      //   })
      // }
      // 重新加载订阅数据和积分数据
      await this.loadSubscriptions(uid, current_company_id, this.appid)
    },

    // 发送反馈（点赞/点踩）
    async sendMessageFeedback(
      messageId: string,
      rating: 'like' | 'dislike' | null,
      content?: string,
    ) {
      if (!user.value || !this.appid) {
        uni.showToast({

          duration: 2000,
        })
        return false
      }

      try {
        // 使用新的API端点
        const feedbackUrl = API.dify.messageFeedback

        // 准备请求数据
        const feedbackData = {
          rating,
          user: user.value.id,
          content: content || '',
          message_id: messageId,
        }

        // 发送请求
        const response = await fetch(feedbackUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-App-ID': this.appid,
          },
          body: JSON.stringify(feedbackData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || 'Failed to send feedback')
        }

        // 更新本地消息状态
        const messageIndex = this.messages.findIndex(
          message => message.id === messageId,
        )

        if (messageIndex !== -1) {
          // 使用类型断言
          (this.messages[messageIndex] as any).feedback = rating
        }

        // 如果在缓存中存在该会话，也更新缓存
        if (this.currentConversationId) {
          const cachedData = this.messageCache.get(this.currentConversationId)
          if (cachedData) {
            const cachedMessageIndex = cachedData.messages.findIndex(
              message => message.id === messageId,
            )
            if (cachedMessageIndex !== -1) {
              // 使用类型断言
              (cachedData.messages[cachedMessageIndex] as any).feedback
                = rating
              this.messageCache.set(this.currentConversationId, cachedData)
            }
          }
        }
        return true
      }
      catch (error: any) {
        console.error('Error sending message feedback:', error)
        uni.showToast({

          title: error.message || '无法发送反馈',
          duration: 2000,
        })
        return false
      }
    },
    async loadMessagesSuggested(lastMessageId: string) {
      if (this.type !== 'chat') { return }

      if (!user.value) { return }

      try {
        const params = {
          user: user.value.id,
        }

        const data = await get(API.dify.messagesSuggested(lastMessageId), params, false, {
          'X-App-ID': this.appid || '',
        })

        this.messagesSuggested = data.data
        console.log('loadMessagesSuggested： ', JSON.stringify(data))
      }
      catch (error: any) {
        console.error('Error loading message suggested:', error)
      }
      finally {
        console.log('loadMessagesSuggested finally')
      }
    },

    // 清空建议问题
    clearMessagesSuggested() {
      this.messagesSuggested = []
    },

    async checkCanUse() {
      const { can_use, code, message } = await chatStore.checkUserCanUseCompanyApp(user.value.id, this.appid)
      if (!can_use) {
        this.rechargeTips = true
        return false
      }
      else {
        return true
      }
    },
  },
})
